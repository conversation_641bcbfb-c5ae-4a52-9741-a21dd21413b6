package com.trs.police.service.node.spark.impl;

import com.trs.police.dto.node.properties.OrderNodeProperties;
import com.trs.police.dto.node.properties.bean.OrderItem;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.util.List;

/**
 * Spark排序节点
 *
 * <AUTHOR>
 */
public class SparkOrderNode extends SparkNode {

    public SparkOrderNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        OrderNodeProperties property = getPropertyAs(OrderNodeProperties.class);
        NodeData input = inputNode.get(0);
        
        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);
        
        // 应用排序
        Dataset<Row> sortedDataset = applySparkSort(dataset, property);
        
        // 转换回NodeData
        NodeData result = datasetToNodeData(sortedDataset, input);
        result.setNodeMeta(nodeMeta);
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.ORDER;
    }

    /**
     * 应用Spark排序
     *
     * @param dataset 数据集
     * @param property 排序属性
     * @return 排序后的数据集
     */
    private Dataset<Row> applySparkSort(Dataset<Row> dataset, OrderNodeProperties property) {
        if (property.getOrderItems() == null || property.getOrderItems().isEmpty()) {
            return dataset;
        }

        // 构建排序表达式
        org.apache.spark.sql.Column[] sortColumns = property.getOrderItems().stream()
                .map(this::buildSortColumn)
                .toArray(org.apache.spark.sql.Column[]::new);

        return dataset.orderBy(sortColumns);
    }

    /**
     * 构建排序列
     *
     * @param orderItem 排序项
     * @return Spark排序列
     */
    private org.apache.spark.sql.Column buildSortColumn(OrderItem orderItem) {
        org.apache.spark.sql.Column column = org.apache.spark.sql.functions.col(orderItem.getEnName());

        // 根据排序方向设置升序或降序，与原始OrderNode逻辑保持一致
        if ("desc".equalsIgnoreCase(orderItem.getOrder())) {
            return column.desc();
        } else {
            return column.asc(); // 默认升序，与原始逻辑一致
        }
    }
}
