# Expression到SQL转换实现

## 概述

本文档详细说明了在`SparkTableNode`中实现的Expression到SQL WHERE子句转换功能。该功能允许将复杂的Expression对象转换为标准的SQL WHERE条件，从而实现过滤条件的数据库层面下推。

## 核心方法

### 1. convertExpressionToSql(Expression expression)

主入口方法，负责将Expression转换为SQL WHERE子句。

```java
private String convertExpressionToSql(Expression expression) {
    if (expression == null || expression instanceof EmtpyExpression) {
        return "";
    }

    try {
        return convertExpressionToSqlRecursive(expression);
    } catch (Exception e) {
        log.warn("Expression到SQL转换失败: {}", e.getMessage());
        return "";
    }
}
```

### 2. convertExpressionToSqlRecursive(Expression expression)

递归转换方法，根据Expression的具体类型进行相应的转换处理。

支持的Expression类型：
- **AndExpression**: 逻辑AND操作
- **OrExpression**: 逻辑OR操作  
- **NotExpression**: 逻辑NOT操作
- **ConditionExpression**: 条件表达式
- **SimpleConditionExpression**: 简单条件表达式
- **EmtpyExpression**: 空表达式

## 支持的逻辑操作

### AND表达式转换

```java
private String convertAndExpression(Expression expression) {
    // 获取左右子表达式
    Expression left = getLeftExpression(expression);
    Expression right = getRightExpression(expression);
    
    String leftSql = convertExpressionToSqlRecursive(left);
    String rightSql = convertExpressionToSqlRecursive(right);
    
    if (StringUtils.isNotEmpty(leftSql) && StringUtils.isNotEmpty(rightSql)) {
        return "(" + leftSql + " AND " + rightSql + ")";
    }
    // 处理单边有效的情况
}
```

**示例转换**：
- 输入：`name = 'John' AND age > 25`
- 输出：`(name = 'John' AND age > 25)`

### OR表达式转换

```java
private String convertOrExpression(Expression expression) {
    // 类似AND表达式，但使用OR连接
    return "(" + leftSql + " OR " + rightSql + ")";
}
```

**示例转换**：
- 输入：`status = 'active' OR priority = 'high'`
- 输出：`(status = 'active' OR priority = 'high')`

### NOT表达式转换

```java
private String convertNotExpression(Expression expression) {
    Expression child = getChildExpression(expression);
    String childSql = convertExpressionToSqlRecursive(child);
    
    if (StringUtils.isNotEmpty(childSql)) {
        return "NOT (" + childSql + ")";
    }
}
```

**示例转换**：
- 输入：`NOT (status = 'deleted')`
- 输出：`NOT (status = 'deleted')`

## 条件表达式转换

### 支持的操作符

| 原始操作符 | SQL操作符 | 说明 |
|-----------|----------|------|
| EQ, EQUAL, EQUALS | = | 等于 |
| NE, NOT_EQUAL | != | 不等于 |
| GT, GREATER_THAN | > | 大于 |
| GE, GREATER_EQUAL | >= | 大于等于 |
| LT, LESS_THAN | < | 小于 |
| LE, LESS_EQUAL | <= | 小于等于 |
| LIKE, CONTAINS | LIKE | 模糊匹配 |
| NOT_LIKE, NOT_CONTAINS | NOT LIKE | 不匹配 |
| IN | IN | 包含于 |
| NOT_IN | NOT IN | 不包含于 |
| BETWEEN | BETWEEN | 范围查询 |
| IS_NULL, NULL | IS NULL | 为空 |
| IS_NOT_NULL, NOT_NULL | IS NOT NULL | 不为空 |

### 条件构建示例

```java
private String buildSqlCondition(String fieldName, String operator, Object[] values) {
    switch (sqlOperator.toUpperCase()) {
        case "=":
            return fieldName + " = " + formatSqlValue(values[0]);
        case "IN":
            return fieldName + " IN (" + joinValues(values) + ")";
        case "BETWEEN":
            return fieldName + " BETWEEN " + formatSqlValue(values[0]) + 
                   " AND " + formatSqlValue(values[1]);
        case "LIKE":
            return fieldName + " LIKE " + formatSqlValue(values[0]);
        // ... 其他操作符
    }
}
```

## 值格式化

### formatSqlValue(Object value)

负责将Java对象转换为SQL兼容的值格式：

```java
private String formatSqlValue(Object value) {
    if (value == null) {
        return "NULL";
    }
    
    if (value instanceof String) {
        // 字符串加单引号，转义内部单引号
        String strValue = value.toString().replace("'", "''");
        return "'" + strValue + "'";
    } else if (value instanceof Number) {
        // 数字直接返回
        return value.toString();
    } else if (value instanceof Boolean) {
        // 布尔值转换为0/1
        return ((Boolean) value) ? "1" : "0";
    } else if (value instanceof Date) {
        // 日期格式化
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return "'" + sdf.format((Date) value) + "'";
    } else {
        // 其他类型转换为字符串
        String strValue = value.toString().replace("'", "''");
        return "'" + strValue + "'";
    }
}
```

## 反射机制

由于Expression接口的具体实现可能来自不同的包，本实现使用反射机制来提取Expression的属性：

### 字段名提取

```java
private String getFieldFromExpression(Expression expression) {
    // 尝试多种可能的字段名属性
    String[] fieldNames = {"field", "fieldName", "key", "name", "attribute"};
    
    for (String fieldName : fieldNames) {
        try {
            Field field = expression.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(expression);
            if (value != null) {
                return value.toString();
            }
        } catch (NoSuchFieldException ignored) {
            // 继续尝试下一个字段名
        }
    }
    
    // 尝试通过getter方法获取
    String[] methodNames = {"getField", "getFieldName", "getKey"};
    // ... 方法调用逻辑
}
```

### 操作符提取

```java
private String getOperatorFromExpression(Expression expression) {
    // 类似字段名提取，但针对操作符属性
    String[] operatorFields = {"operator", "op", "operation"};
    // ... 提取逻辑
}
```

### 值提取

```java
private Object[] getValuesFromExpression(Expression expression) {
    // 支持数组、List和单个值的提取
    String[] valueFields = {"values", "value", "data", "params"};
    // ... 提取逻辑
}
```

## 转换示例

### 简单条件

**输入Expression**：
```
field: "name"
operator: "EQ" 
values: ["John"]
```

**输出SQL**：
```sql
name = 'John'
```

### 复合条件

**输入Expression**：
```
AND {
  left: { field: "age", operator: "GT", values: [25] }
  right: { field: "status", operator: "IN", values: ["active", "pending"] }
}
```

**输出SQL**：
```sql
(age > 25 AND status IN ('active', 'pending'))
```

### 复杂嵌套条件

**输入Expression**：
```
OR {
  left: AND {
    left: { field: "name", operator: "LIKE", values: ["%John%"] }
    right: { field: "age", operator: "BETWEEN", values: [25, 35] }
  }
  right: { field: "priority", operator: "EQ", values: ["high"] }
}
```

**输出SQL**：
```sql
((name LIKE '%John%' AND age BETWEEN 25 AND 35) OR priority = 'high')
```

## 错误处理

1. **空值处理**: 对null和EmptyExpression返回空字符串
2. **反射失败**: 捕获反射异常，记录调试日志，继续处理
3. **未知操作符**: 记录警告日志，跳过该条件
4. **格式错误**: 返回空字符串，触发回退机制

## 性能考虑

1. **缓存机制**: 可以考虑对常用的Expression模式进行缓存
2. **反射优化**: 使用字段/方法缓存减少反射开销
3. **字符串构建**: 使用StringBuilder进行字符串拼接

## 扩展性

该实现具有良好的扩展性：

1. **新操作符支持**: 在`convertOperatorToSql`方法中添加新的映射
2. **新数据类型**: 在`formatSqlValue`方法中添加新的格式化逻辑
3. **新Expression类型**: 在`convertExpressionToSqlRecursive`中添加新的case分支

## 使用建议

1. **测试验证**: 建议对各种Expression组合进行充分测试
2. **日志监控**: 关注转换失败的日志，及时优化
3. **性能监控**: 监控SQL查询性能，确保转换后的SQL高效执行
4. **安全考虑**: 注意SQL注入防护，特别是字符串值的转义处理
