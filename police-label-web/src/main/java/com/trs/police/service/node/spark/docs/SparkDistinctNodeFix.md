# SparkDistinctNode 去重逻辑修复

## 问题描述

原本的 `SparkDistinctNode` 实现与 `DistinctNode` 的去重逻辑不一致，主要问题包括：

1. **错误的属性使用**: 使用了 `GroupField` 而不是 `DistinctField`
2. **缺失排序逻辑**: 没有正确处理 `DistinctRule` 的排序规则
3. **去重策略不匹配**: 去重类型判断逻辑与原本实现不符

## 原本DistinctNode的逻辑

### 核心流程

```java
// 1. 构建分组字段列表
List<GroupField> groupFieldList = property.getGroupField()
    .stream()
    .map(field -> new GroupField(field.getFiled(), field.getDistinctType()))
    .collect(Collectors.toList());

// 2. 按分组字段进行分组
Map<String, List<List<FieldValue>>> group = input.getData()
    .stream()
    .collect(Collectors.groupingBy(GroupHelper.getGroupKey(groupFieldList)));

// 3. 构建排序规则
List<OrderItem> ods = property.getDistinctRule()
    .stream()
    .map(rule -> {
        OrderItem orderItem = new OrderItem();
        orderItem.setFromNode(rule.getFiled().getFromNode());
        orderItem.setEnName(rule.getFiled().getEnName());
        orderItem.setOrder(rule.getOrder());
        return orderItem;
    })
    .collect(Collectors.toList());

// 4. 组内排序
for (List<List<FieldValue>> value : group.values()) {
    value.sort(comparable);
}

// 5. 每组保留第一条记录
nodeData.setData(group.values()
    .stream()
    .map(list -> list.get(0))
    .collect(Collectors.toList()));
```

### 关键数据结构

- **DistinctField**: 去重字段，包含 `distinctType` 属性
- **DistinctRule**: 排序规则，包含 `order` 属性（ASC/DESC）
- **GroupField**: 分组字段，由 DistinctField 转换而来

## 修复后的SparkDistinctNode逻辑

### 核心改进

1. **正确的属性映射**: 
   ```java
   // 使用DistinctField转换为GroupField，保持与原逻辑一致
   List<GroupField> groupFieldList = property.getGroupField().stream()
       .map(field -> new GroupField(field.getFiled(), field.getDistinctType()))
       .collect(Collectors.toList());
   ```

2. **完整的排序支持**:
   ```java
   // 构建排序列，支持ASC/DESC
   private List<org.apache.spark.sql.Column> buildOrderColumns(List<DistinctRule> distinctRules) {
       return distinctRules.stream()
           .map(rule -> {
               org.apache.spark.sql.Column column = functions.col(rule.getEnName());
               return rule.isDesc() ? column.desc() : column.asc();
           })
           .collect(Collectors.toList());
   }
   ```

3. **窗口函数实现分组去重**:
   ```java
   // 使用Spark窗口函数实现与原逻辑相同的效果
   private Dataset<Row> applyWindowBasedDistinctWithSort(Dataset<Row> dataset,
                                                        List<GroupField> groupFields,
                                                        List<org.apache.spark.sql.Column> orderColumns) {
       // 构建分组列
       org.apache.spark.sql.Column[] partitionColumns = groupFields.stream()
           .map(field -> functions.col(field.getEnName()))
           .toArray(org.apache.spark.sql.Column[]::new);

       // 构建窗口规范
       WindowSpec windowSpec = Window.partitionBy(partitionColumns);
       
       // 添加排序
       if (!orderColumns.isEmpty()) {
           windowSpec = windowSpec.orderBy(orderColumns.toArray(new org.apache.spark.sql.Column[0]));
       }

       // 使用row_number()窗口函数，每组保留第一条记录
       return dataset.withColumn("rn", functions.row_number().over(windowSpec))
               .filter(functions.col("rn").equalTo(1))
               .drop("rn");
   }
   ```

## 逻辑对比

### 原本DistinctNode
```
输入数据 → 按字段分组 → 组内排序 → 每组取第一条 → 输出结果
```

### 修复后SparkDistinctNode
```
输入数据 → 转换为Dataset → 窗口函数(分组+排序) → row_number()=1 → 转换回NodeData → 输出结果
```

## 具体修复内容

### 1. 导入修复
```java
// 添加必要的导入
import com.trs.police.dto.node.properties.bean.DistinctRule;
import org.apache.spark.sql.expressions.Window;
import org.apache.spark.sql.expressions.WindowSpec;
import lombok.extern.slf4j.Slf4j;
```

### 2. 方法重构

**原来的错误实现**:
```java
// 错误：直接使用GroupField.getField()
List<String> distinctFields = property.getGroupField().stream()
    .map(GroupField::getField)  // 这个方法不存在
    .collect(Collectors.toList());
```

**修复后的正确实现**:
```java
// 正确：按照原逻辑转换DistinctField为GroupField
List<GroupField> groupFieldList = property.getGroupField().stream()
    .map(field -> new GroupField(field.getFiled(), field.getDistinctType()))
    .collect(Collectors.toList());
```

### 3. 排序逻辑实现

**原来缺失的排序处理**:
```java
// 原来没有处理DistinctRule的排序逻辑
```

**修复后的排序处理**:
```java
// 构建排序字段列表 - 使用DistinctRule
List<org.apache.spark.sql.Column> orderColumns = buildOrderColumns(property.getDistinctRule());

private List<org.apache.spark.sql.Column> buildOrderColumns(List<DistinctRule> distinctRules) {
    if (distinctRules == null || distinctRules.isEmpty()) {
        // 如果没有排序规则，使用单调递增ID作为默认排序
        return List.of(functions.monotonically_increasing_id().asc());
    }

    return distinctRules.stream()
        .map(rule -> {
            org.apache.spark.sql.Column column = functions.col(rule.getEnName());
            return rule.isDesc() ? column.desc() : column.asc();
        })
        .collect(Collectors.toList());
}
```

## 性能优势

修复后的SparkDistinctNode相比原本DistinctNode具有以下优势：

1. **分布式处理**: 利用Spark的分布式计算能力
2. **内存优化**: Spark的懒加载和内存管理
3. **并行执行**: 窗口函数可以并行处理不同分区
4. **SQL优化**: Spark的Catalyst优化器自动优化执行计划

## 使用示例

### 配置示例
```json
{
  "groupField": [
    {
      "fromNode": "node1",
      "cnName": "姓名",
      "enName": "name",
      "typeCode": "STRING",
      "distinctType": 1
    }
  ],
  "distinctRule": [
    {
      "fromNode": "node1",
      "cnName": "创建时间",
      "enName": "create_time",
      "typeCode": "DATETIME",
      "order": "DESC"
    }
  ]
}
```

### 执行效果
```
输入数据:
name | age | create_time
-----|-----|------------
John | 25  | 2024-01-01
John | 26  | 2024-01-02
Jane | 30  | 2024-01-01
Jane | 31  | 2024-01-03

去重结果 (按name分组，按create_time降序排序，每组取第一条):
name | age | create_time
-----|-----|------------
John | 26  | 2024-01-02
Jane | 31  | 2024-01-03
```

## 测试建议

1. **功能测试**: 验证去重结果与原本DistinctNode一致
2. **性能测试**: 对比大数据量下的处理性能
3. **边界测试**: 测试空数据、单条数据、重复数据等边界情况
4. **配置测试**: 测试不同的distinctType和order配置组合

## 兼容性

- ✅ 完全兼容原本DistinctNode的配置格式
- ✅ 保持相同的输入输出接口
- ✅ 支持所有原有的去重和排序规则
- ✅ 向后兼容现有的节点配置
