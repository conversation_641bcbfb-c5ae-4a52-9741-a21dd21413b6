package com.trs.police.service.node.spark.impl;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.entity.node.ControlValue;
import com.trs.police.common.core.entity.node.TableFieldValue;
import com.trs.police.common.core.entity.node.Value;
import com.trs.police.common.core.entity.node.comparable.ComparableValue;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.ValueWrapper;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.properties.FilterNodeProperties;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.spark.SparkNode;
import org.apache.spark.sql.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Stack;

/**
 * Spark过滤节点
 *
 * <AUTHOR>
 */
public class SparkFilterNode extends SparkNode {

    public SparkFilterNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        FilterNodeProperties property = getPropertyAs(FilterNodeProperties.class);
        NodeData input = inputNode.get(0);

        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);

        // 应用Spark过滤条件
        Dataset<Row> filteredDataset = applySparkFilter(dataset, property, context, input.getHeader());

        // 转换回NodeData
        NodeData result = datasetToNodeData(filteredDataset, input);
        result.setNodeMeta(nodeMeta);

        return result;
    }

    /**
     * 应用Spark过滤条件
     * 将原始FilterNode的逻辑转换为Spark SQL表达式
     *
     * @param dataset  数据集
     * @param property 过滤属性
     * @param context  上下文
     * @param headers  表头信息
     * @return 过滤后的数据集
     */
    private Dataset<Row> applySparkFilter(Dataset<Row> dataset, FilterNodeProperties property,
                                          NodeContext context, List<FieldInfoVO> headers) {
        if (property.getTokens() == null || property.getTokens().length == 0) {
            return dataset;
        }

        // 解析tokens并构建Spark过滤条件
        Column filterCondition = parseTokensToSparkCondition(property.getTokens(), context, headers);

        if (filterCondition != null) {
            return dataset.filter(filterCondition);
        }

        return dataset;
    }

    /**
     * 解析tokens并构建Spark过滤条件
     * 使用栈结构处理逻辑操作符优先级，与原始FilterNode逻辑一致
     *
     * @param tokens  过滤tokens
     * @param context 上下文
     * @param headers 表头信息
     * @return Spark过滤条件
     */
    private Column parseTokensToSparkCondition(String[] tokens, NodeContext context, List<FieldInfoVO> headers) {
        Stack<Column> conditionStack = new Stack<>();
        Stack<String> operatorStack = new Stack<>();

        for (String token : tokens) {
            if ("(".equals(token)) {
                operatorStack.push(token);
            } else if (")".equals(token)) {
                // 处理括号内的表达式
                while (!operatorStack.isEmpty() && !"(".equals(operatorStack.peek())) {
                    applyLogicalOperator(conditionStack, operatorStack.pop());
                }
                if (!operatorStack.isEmpty() && "(".equals(operatorStack.peek())) {
                    operatorStack.pop(); // 移除左括号
                }
            } else if ("且".equals(token) || "或".equals(token) || "非".equals(token)) {
                // 处理逻辑操作符优先级
                while (!operatorStack.isEmpty() && !"(".equals(operatorStack.peek()) &&
                        getOperatorPrecedence(operatorStack.peek()) >= getOperatorPrecedence(token)) {
                    applyLogicalOperator(conditionStack, operatorStack.pop());
                }
                operatorStack.push(token);
            } else {
                // 处理条件对象
                try {
                    FilterNodeProperties.Condition condition = JSON.parseObject(token, FilterNodeProperties.Condition.class);
                    Column sparkCondition = buildSparkCondition(condition, context, headers);
                    if (sparkCondition != null) {
                        conditionStack.push(sparkCondition);
                    }
                } catch (Exception e) {
                    // 如果解析失败，跳过该条件
                    System.err.println("Failed to parse condition: " + token + ", error: " + e.getMessage());
                }
            }
        }

        // 处理剩余的操作符
        while (!operatorStack.isEmpty()) {
            applyLogicalOperator(conditionStack, operatorStack.pop());
        }

        return conditionStack.isEmpty() ? null : conditionStack.pop();
    }

    @Override
    public Integer nodeType() {
        return NodeType.FILTER;
    }

    /**
     * 构建单个Spark过滤条件
     *
     * @param condition 条件对象
     * @param context   上下文
     * @param headers   表头信息
     * @return Spark过滤条件
     */
    private Column buildSparkCondition(FilterNodeProperties.Condition condition,
                                                            NodeContext context, List<FieldInfoVO> headers) {
        String key = condition.getKey();
        String operator = condition.getOperator();
        ValueWrapper values = condition.getValue();

        // 获取字段列
        Column fieldColumn = functions.col(key);

        // 根据操作符构建不同的Spark条件
        switch (operator) {
            case "in":
                return buildInCondition(fieldColumn, values);
            case "notIn":
                return functions.not(buildInCondition(fieldColumn, values));
            case "like":
                return buildLikeCondition(fieldColumn, values);
            case "notLike":
                return functions.not(buildLikeCondition(fieldColumn, values));
            case "isNull":
                return fieldColumn.isNull();
            case "notNull":
                return fieldColumn.isNotNull();
            case "empty":
                return fieldColumn.equalTo("").or(fieldColumn.isNull());
            case "notEmpty":
                return fieldColumn.notEqual("").and(fieldColumn.isNotNull());
            case "regularMatch":
                return buildRegexCondition(fieldColumn, values);
            case "regularNotMatch":
                return functions.not(buildRegexCondition(fieldColumn, values));
            case "eq":
                return buildComparisonCondition(fieldColumn, values, "eq", context);
            case "ne":
                return buildComparisonCondition(fieldColumn, values, "ne", context);
            case "gt":
                return buildComparisonCondition(fieldColumn, values, "gt", context);
            case "lt":
                return buildComparisonCondition(fieldColumn, values, "lt", context);
            case "ge":
                return buildComparisonCondition(fieldColumn, values, "ge", context);
            case "le":
                return buildComparisonCondition(fieldColumn, values, "le", context);
            default:
                return functions.lit(true); // 默认返回true
        }
    }

    /**
     * 构建IN条件
     */
    private Column buildInCondition(Column fieldColumn, ValueWrapper values) {
        List<String> valueList = new ArrayList<>();
        for (int i = 0; i < values.getValue().length; i++) {
            Value value = values.getTargetValue(i);
            if (value != null && value.getValueString() != null) {
                valueList.add(value.getValueString());
            }
        }

        if (valueList.isEmpty()) {
            return functions.lit(false);
        }

        return fieldColumn.isin(valueList.toArray());
    }

    /**
     * 构建LIKE条件
     */
    private Column buildLikeCondition(Column fieldColumn, ValueWrapper values) {
        Value value = values.getTargetValue(0);
        if (value != null && value.getValueString() != null) {
            String pattern = "%" + value.getValueString() + "%";
            return fieldColumn.like(pattern);
        }
        return functions.lit(false);
    }

    /**
     * 构建正则表达式条件
     */
    private Column buildRegexCondition(Column fieldColumn, ValueWrapper values) {
        Value value = values.getTargetValue(0);
        if (value != null && value.getValueString() != null) {
            return fieldColumn.rlike(value.getValueString());
        }
        return functions.lit(false);
    }

    /**
     * 构建比较条件
     */
    private Column buildComparisonCondition(Column fieldColumn,
                                                                 ValueWrapper values, String operator, NodeContext context) {
        Value value = values.getTargetValue(0);
        if (value == null) {
            return functions.lit(false);
        }

        // 处理不同类型的值
        if (value instanceof ComparableValue) {
            return buildComparableCondition(fieldColumn, (ComparableValue) value, operator);
        } else if (value instanceof TableFieldValue) {
            return buildFieldComparisonCondition(fieldColumn, (TableFieldValue) value, operator);
        } else if (value instanceof ControlValue) {
            return buildControlParameterCondition(fieldColumn, (ControlValue) value, operator, context);
        } else {
            // 普通值比较
            String valueString = value.getValueString();
            return buildSimpleComparison(fieldColumn, valueString, operator);
        }
    }

    /**
     * 构建可比较值条件
     */
    private Column buildComparableCondition(Column fieldColumn,
                                                                 ComparableValue value, String operator) {
        String valueString = value.getValueString();
        return buildSimpleComparison(fieldColumn, valueString, operator);
    }

    /**
     * 构建字段间比较条件
     */
    private Column buildFieldComparisonCondition(Column fieldColumn,
                                                                      TableFieldValue tableFieldValue, String operator) {
        Column otherFieldColumn = functions.col(tableFieldValue.getFiledName());

        switch (operator) {
            case "eq":
                return fieldColumn.equalTo(otherFieldColumn);
            case "ne":
                return fieldColumn.notEqual(otherFieldColumn);
            case "gt":
                return fieldColumn.gt(otherFieldColumn);
            case "lt":
                return fieldColumn.lt(otherFieldColumn);
            case "ge":
                return fieldColumn.geq(otherFieldColumn);
            case "le":
                return fieldColumn.leq(otherFieldColumn);
            default:
                return functions.lit(true);
        }
    }

    /**
     * 构建控制参数条件
     */
    private Column buildControlParameterCondition(Column fieldColumn,
                                                                       ControlValue controlValue, String operator, NodeContext context) {
        Optional<ValueWrapper> valueWrapper = context.getValue(controlValue.getName());
        if (valueWrapper.isEmpty()) {
            return functions.lit(true);
        }

        Value value = valueWrapper.get().getTargetValue(0);
        if (value instanceof ComparableValue) {
            return buildComparableCondition(fieldColumn, (ComparableValue) value, operator);
        } else {
            String valueString = value.getValueString();
            return buildSimpleComparison(fieldColumn, valueString, operator);
        }
    }

    /**
     * 构建简单比较条件
     */
    private Column buildSimpleComparison(Column fieldColumn,
                                                              String value, String operator) {
        if (value == null) {
            return functions.lit(false);
        }

        // 尝试转换为数字进行比较
        Column valueColumn;
        try {
            if (value.contains(".")) {
                double doubleValue = Double.parseDouble(value);
                valueColumn = functions.lit(doubleValue);
                fieldColumn = fieldColumn.cast("double");
            } else {
                long longValue = Long.parseLong(value);
                valueColumn = functions.lit(longValue);
                fieldColumn = fieldColumn.cast("long");
            }
        } catch (NumberFormatException e) {
            // 如果不是数字，按字符串比较
            valueColumn = functions.lit(value);
        }

        switch (operator) {
            case "eq":
                return fieldColumn.equalTo(valueColumn);
            case "ne":
                return fieldColumn.notEqual(valueColumn);
            case "gt":
                return fieldColumn.gt(valueColumn);
            case "lt":
                return fieldColumn.lt(valueColumn);
            case "ge":
                return fieldColumn.geq(valueColumn);
            case "le":
                return fieldColumn.leq(valueColumn);
            default:
                return functions.lit(true);
        }
    }

    /**
     * 应用逻辑操作符
     */
    private void applyLogicalOperator(Stack<Column> conditionStack, String operator) {
        if ("非".equals(operator)) {
            if (!conditionStack.isEmpty()) {
                Column condition = conditionStack.pop();
                conditionStack.push(functions.not(condition));
            }
        } else {
            if (conditionStack.size() >= 2) {
                Column right = conditionStack.pop();
                Column left = conditionStack.pop();

                if ("且".equals(operator)) {
                    conditionStack.push(left.and(right));
                } else if ("或".equals(operator)) {
                    conditionStack.push(left.or(right));
                }
            }
        }
    }

    /**
     * 获取操作符优先级
     */
    private int getOperatorPrecedence(String operator) {
        switch (operator) {
            case "非":
                return 3; // 最高优先级
            case "且":
                return 2; // 中等优先级
            case "或":
                return 1; // 最低优先级
            default:
                return 0;
        }
    }
}
