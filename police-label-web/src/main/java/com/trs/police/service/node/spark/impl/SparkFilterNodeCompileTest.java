package com.trs.police.service.node.spark.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.properties.FilterNodeProperties;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import org.apache.spark.sql.SparkSession;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * SparkFilterNode编译测试
 * 验证修复后的代码能够正常编译和运行
 * 
 * <AUTHOR>
 */
public class SparkFilterNodeCompileTest {

    public static void main(String[] args) {
        System.out.println("开始SparkFilterNode编译测试...");
        
        try {
            // 创建Spark会话
            SparkSession spark = SparkSession.builder()
                    .appName("SparkFilterNodeCompileTest")
                    .master("local[*]")
                    .config("spark.sql.warehouse.dir", "/tmp/spark-warehouse")
                    .getOrCreate();

            // 创建节点元数据
            NodeMeta nodeMeta = new NodeMeta();
            nodeMeta.setUuid("test-filter-node");
            nodeMeta.setName("测试过滤节点");

            // 创建过滤属性
            FilterNodeProperties properties = new FilterNodeProperties();
            properties.setTokens(new String[]{
                "{\"key\":\"age\",\"operator\":\"gt\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"25\"]}}"
            });

            String propertiesJson = JSONObject.toJSONString(properties);

            // 创建SparkFilterNode实例
            SparkFilterNode sparkFilterNode = new SparkFilterNode(nodeMeta, propertiesJson, spark);
            System.out.println("✓ SparkFilterNode实例创建成功");

            // 创建测试数据
            NodeData testData = createTestData();
            System.out.println("✓ 测试数据创建成功");

            // 执行过滤
            List<NodeData> inputNodes = Arrays.asList(testData);
            NodeContext context = new NodeContext(new ArrayList<>());

            NodeData result = sparkFilterNode.process(inputNodes, context);
            System.out.println("✓ 过滤处理执行成功");

            // 验证结果
            if (result != null && result.getData() != null) {
                System.out.println("✓ 过滤结果验证成功");
                System.out.println("原始数据行数: " + testData.getData().size());
                System.out.println("过滤后行数: " + result.getData().size());
                
                // 验证过滤逻辑
                for (List<FieldValue> row : result.getData()) {
                    FieldValue ageField = row.stream()
                            .filter(f -> "age".equals(f.getEnName()))
                            .findFirst()
                            .orElse(null);
                    if (ageField != null) {
                        int age = Integer.parseInt(ageField.getValue());
                        if (age <= 25) {
                            System.err.println("✗ 过滤逻辑错误: 发现age <= 25的记录: " + age);
                            return;
                        }
                    }
                }
                System.out.println("✓ 过滤逻辑验证成功");
            } else {
                System.err.println("✗ 过滤结果为空");
                return;
            }

            spark.stop();
            System.out.println("✓ Spark会话关闭成功");
            
            System.out.println("\n🎉 所有测试通过！SparkFilterNode编译和运行正常。");
            
        } catch (Exception e) {
            System.err.println("✗ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建测试数据
     */
    private static NodeData createTestData() {
        NodeData nodeData = new NodeData();
        
        // 创建表头
        List<FieldInfoVO> headers = Arrays.asList(
            createFieldInfo("name", "姓名", 0),
            createFieldInfo("age", "年龄", 1),
            createFieldInfo("department", "部门", 2)
        );
        nodeData.setHeader(headers);
        
        // 创建数据行
        List<List<FieldValue>> data = Arrays.asList(
            createDataRow("张三", "28", "IT"),
            createDataRow("李四", "22", "HR"),
            createDataRow("王五", "35", "Finance"),
            createDataRow("赵六", "24", "IT"),
            createDataRow("钱七", "30", "HR")
        );
        nodeData.setData(data);
        nodeData.setTotalCount((long) data.size());
        
        return nodeData;
    }

    private static FieldInfoVO createFieldInfo(String enName, String cnName, int colIndex) {
        FieldInfoVO fieldInfo = new FieldInfoVO();
        fieldInfo.setEnName(enName);
        fieldInfo.setCnName(cnName);
        fieldInfo.setColIndex(colIndex);
        fieldInfo.setTypeCode("1");
        return fieldInfo;
    }

    private static List<FieldValue> createDataRow(String... values) {
        List<FieldValue> row = new ArrayList<>();
        String[] fieldNames = {"name", "age", "department"};
        
        for (int i = 0; i < values.length; i++) {
            FieldValue fieldValue = new FieldValue();
            fieldValue.setEnName(fieldNames[i]);
            fieldValue.setValue(values[i]);
            fieldValue.setColIndex(i);
            fieldValue.setTypeCode("1");
            row.add(fieldValue);
        }
        return row;
    }
}
