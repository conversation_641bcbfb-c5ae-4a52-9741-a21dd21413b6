# SparkTableNode 数据查询优化

## 概述

本次修改将 `SparkTableNode` 中的数据查询方式从通过 `DataTableService.getData()` 改为直接通过 Spark JDBC 查询数据库，以充分利用 Spark 的分布式查询能力。

## 修改内容

### 原始实现问题

```java
// 原始代码 - 第78行
RestfulResultsV2<JSONObject> dataOverview = dataTableService.getData(dto, expression);
List<JSONObject> datas = dataOverview.getDatas();

// 使用Spark处理数据
Dataset<Row> sparkDataset = createSparkDatasetFromJsonData(datas, header);
```

**问题**：
1. 先通过 `DataTableService` 查询数据库获取 JSON 数据
2. 再将 JSON 数据转换为 Spark Dataset
3. 没有充分利用 Spark 的分布式查询能力
4. 数据传输效率低

### 优化后的实现

```java
try {
    // 通过Spark直接查询数据库
    Dataset<Row> sparkDataset = queryDataDirectlyWithSpark(property.getTableId(), expression);
    
    // 缓存数据集以提高性能
    sparkDataset = sparkDataset.cache();
    
    // 转换回NodeData格式
    NodeData result = datasetToNodeData(sparkDataset, nodeData);
    result.setTotalCount(sparkDataset.count());
    
    return result;
} catch (Exception e) {
    log.warn("Spark直接查询失败，回退到原始方式: {}", e.getMessage());
    // 回退到原始方式
    return processWithFallback(property, header, expression, nodeData);
}
```

## 新增功能

### 1. 直接数据库查询

`queryDataDirectlyWithSpark()` 方法实现了：
- 获取数据表和数据源信息
- 构建 JDBC 连接字符串
- 使用 Spark JDBC 直接查询数据库
- 支持过滤条件下推

### 2. 多数据库支持

支持以下数据库类型：
- **MySQL**: `*******************************`
- **Oracle**: `************************************`
- **PostgreSQL**: `************************************`
- **ClickHouse**: `************************************`

### 3. 连接属性优化

```java
private Properties buildConnectionProperties(AbstractDbSourceInfo dbSourceInfo) {
    Properties props = new Properties();
    props.setProperty("user", dbSourceInfo.getUserName());
    props.setProperty("password", dbSourceInfo.getPassword());
    
    // 设置连接池相关属性
    props.setProperty("numPartitions", "4");
    props.setProperty("fetchsize", "1000");
    
    return props;
}
```

### 4. 错误回退机制

如果 Spark 直接查询失败，会自动回退到原始的 `DataTableService.getData()` 方式，确保系统稳定性。

## 性能优势

1. **减少数据传输**: 直接在数据库层面进行查询，避免中间数据转换
2. **分布式处理**: 利用 Spark 的分布式查询能力
3. **连接池优化**: 设置合适的分区数和批次大小
4. **数据缓存**: 对查询结果进行缓存以提高后续访问性能

## 待完善功能

### Expression 到 SQL 转换

目前 `convertExpressionToSql()` 方法暂未实现完整的转换逻辑：

```java
private String convertExpressionToSql(Expression expression) {
    if (expression == null || expression instanceof EmtpyExpression) {
        return "";
    }
    
    // TODO: 实现Expression到SQL的转换逻辑
    log.warn("Expression到SQL转换暂未实现，将不应用过滤条件");
    return "";
}
```

**建议后续实现**：
- 解析 Expression 对象结构
- 转换为标准 SQL WHERE 子句
- 支持复杂的逻辑组合条件

## 使用示例

```java
// 启用Spark后，SparkTableNode会自动使用新的查询方式
TableNodeProperties property = new TableNodeProperties();
property.setTableId(123L);
property.setTokens("过滤条件");

// 系统会自动尝试Spark直接查询，失败时回退到原始方式
NodeData result = sparkTableNode.process(inputNodes, context);
```

## 配置要求

确保在 `application-spark.yml` 中正确配置：

```yaml
spark:
  enabled: true
  app:
    name: police-label-spark
  master: local[*]
  executor:
    memory: 2g
    cores: 2
  driver:
    memory: 1g
```

## 兼容性

- 完全向后兼容原有功能
- 自动错误回退机制
- 不影响现有的 `DataTableService` 使用
