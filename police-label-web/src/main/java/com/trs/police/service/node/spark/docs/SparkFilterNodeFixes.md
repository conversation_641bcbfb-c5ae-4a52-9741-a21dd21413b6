# SparkFilterNode 编译错误修复

## 问题描述

在实现纯Spark版本的`SparkFilterNode`时，遇到了`.not()`方法的编译错误。这是因为在Spark SQL中，`Column`对象没有直接的`.not()`方法。

## 错误原因

```java
// 错误的写法
return buildInCondition(fieldColumn, values).not();
return condition.not();
```

在Spark SQL中，`Column`类没有`.not()`实例方法，需要使用`functions.not()`静态方法来包装条件。

## 修复方案

### 1. 添加正确的import

```java
import org.apache.spark.sql.functions;
```

### 2. 使用functions.not()替代.not()

**修复前：**
```java
case "notIn":
    return buildInCondition(fieldColumn, values).not();
case "notLike":
    return buildLikeCondition(fieldColumn, values).not();
case "regularNotMatch":
    return buildRegexCondition(fieldColumn, values).not();

// 逻辑操作符
conditionStack.push(condition.not());
```

**修复后：**
```java
case "notIn":
    return functions.not(buildInCondition(fieldColumn, values));
case "notLike":
    return functions.not(buildLikeCondition(fieldColumn, values));
case "regularNotMatch":
    return functions.not(buildRegexCondition(fieldColumn, values));

// 逻辑操作符
conditionStack.push(functions.not(condition));
```

### 3. 统一使用functions简化引用

为了保持代码一致性，将所有`org.apache.spark.sql.functions`的调用统一简化为`functions`：

```java
// 统一的写法
functions.col(fieldName)
functions.lit(value)
functions.not(condition)
```

## 修复的具体位置

### 1. 操作符处理
- `notIn` 操作符
- `notLike` 操作符  
- `regularNotMatch` 操作符

### 2. 逻辑操作符
- `非` 操作符的处理

### 3. 辅助方法
- 所有返回`lit(false)`和`lit(true)`的地方
- 所有使用`col()`的地方

## 验证方法

创建了`SparkFilterNodeCompileTest`类来验证修复：

```java
public class SparkFilterNodeCompileTest {
    public static void main(String[] args) {
        // 1. 创建SparkFilterNode实例
        // 2. 创建测试数据
        // 3. 执行过滤操作
        // 4. 验证结果正确性
    }
}
```

## Spark SQL Column API 说明

### 正确的否定操作方式

```java
// ✓ 正确：使用functions.not()
Column condition = col("age").gt(25);
Column negated = functions.not(condition);

// ✗ 错误：Column没有.not()方法
Column negated = condition.not(); // 编译错误
```

### 常用的Column操作

```java
// 比较操作
col("age").equalTo(25)
col("age").gt(25)
col("age").lt(25)
col("age").geq(25)
col("age").leq(25)
col("age").notEqual(25)

// 逻辑操作
condition1.and(condition2)
condition1.or(condition2)
functions.not(condition)

// 空值检查
col("field").isNull()
col("field").isNotNull()

// 字符串操作
col("name").like("%pattern%")
col("name").rlike("regex")

// 集合操作
col("status").isin("active", "pending")
```

## 最佳实践

1. **统一import**: 使用`import org.apache.spark.sql.functions`
2. **一致性**: 所有functions调用使用简化形式
3. **可读性**: 复杂条件使用变量存储中间结果
4. **测试**: 每个修复都要有对应的测试验证

## 性能影响

这些修复不会影响性能，只是API调用方式的改变：
- `functions.not(condition)` 和假想的 `condition.not()` 性能相同
- Spark的Catalyst优化器会优化这些表达式
- 生成的执行计划完全一致

## 总结

通过正确使用Spark SQL的Column API，成功修复了所有编译错误，确保了`SparkFilterNode`的纯Spark实现能够正常编译和运行。这个修复保持了代码的功能完整性，同时遵循了Spark SQL的最佳实践。
