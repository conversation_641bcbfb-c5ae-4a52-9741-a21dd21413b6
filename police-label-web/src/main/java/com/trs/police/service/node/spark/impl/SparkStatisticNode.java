package com.trs.police.service.node.spark.impl;

import com.trs.police.constant.StatisticType;
import com.trs.police.dto.node.properties.StatisticNodeProperties;
import com.trs.police.dto.node.properties.bean.GroupField;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.police.constant.StatisticType.*;

/**
 * Spark统计节点
 *
 * <AUTHOR>
 */
public class SparkStatisticNode extends SparkNode {

    public SparkStatisticNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        StatisticNodeProperties property = getPropertyAs(StatisticNodeProperties.class);
        NodeData input = inputNode.get(0);
        
        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);
        
        // 应用统计操作
        Dataset<Row> statisticDataset = applySparkStatistic(dataset, property);
        
        // 转换回NodeData
        NodeData result = datasetToNodeData(statisticDataset, input);
        result.setNodeMeta(nodeMeta);
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.STATISTIC;
    }

    /**
     * 应用Spark统计操作
     *
     * @param dataset 数据集
     * @param property 统计属性
     * @return 统计后的数据集
     */
    private Dataset<Row> applySparkStatistic(Dataset<Row> dataset, StatisticNodeProperties property) {
        // 获取分组字段列表（支持多个分组字段）
        List<String> groupFields = property.getGroupField().stream()
                .map(GroupField::getField)
                .collect(Collectors.toList());

        // 获取统计字段和类型
        String statisticField = property.getStatisticValue().getEnName();
        String outputField = property.getOutValue().getEnName();
        StatisticType statisticType = StatisticType.of(property.getStatisticType());

        Dataset<Row> result;

        if (groupFields.isEmpty()) {
            // 无分组的统计
            result = applyGlobalStatistic(dataset, statisticField, outputField, statisticType);
        } else {
            // 分组统计（支持多个分组字段，如：按地区+年份分组统计）
            result = applyGroupedStatistic(dataset, groupFields, statisticField, outputField, statisticType, property);
        }

        return result;
    }

    /**
     * 应用全局统计（无分组）
     *
     * @param dataset 数据集
     * @param statisticField 统计字段
     * @param outputField 输出字段名
     * @param statisticType 统计类型
     * @return 统计结果
     */
    private Dataset<Row> applyGlobalStatistic(Dataset<Row> dataset, String statisticField, 
                                            String outputField, StatisticType statisticType) {
        switch (statisticType) {
            case COUNT:
                return dataset.agg(functions.count(statisticField).alias(outputField));
            case SUM:
                return dataset.agg(functions.sum(statisticField).alias(outputField));
            case AVG:
                return dataset.agg(functions.avg(statisticField).alias(outputField));
            case MAX:
                return dataset.agg(functions.max(statisticField).alias(outputField));
            case MIN:
                return dataset.agg(functions.min(statisticField).alias(outputField));
            default:
                throw new RuntimeException("不支持的统计类型: " + statisticType);
        }
    }

    /**
     * 应用分组统计
     *
     * 支持多个分组字段的统计，包括时间字段的特殊分组类型：
     * - 普通字段：按字段值直接分组
     * - 时间字段：支持按秒、分、时、天、周、月、年分组
     * - 多个分组字段：按所有字段的组合分组
     *
     * 注意：原始实现使用GroupHelper.getGroupKey将多个分组字段连接成字符串键
     * Spark实现直接使用多列分组，性能更好
     *
     * @param dataset 数据集
     * @param groupFields 分组字段列表（可能包含多个字段）
     * @param statisticField 统计字段
     * @param outputField 输出字段名
     * @param statisticType 统计类型
     * @return 统计结果，包含所有分组字段和统计结果字段
     */
    private Dataset<Row> applyGroupedStatistic(Dataset<Row> dataset, List<String> groupFields,
                                             String statisticField, String outputField, StatisticType statisticType,
                                             StatisticNodeProperties property) {
        // 验证分组字段是否存在
        validateGroupFields(dataset, groupFields);

        // 处理时间字段的特殊分组逻辑（如果需要）
        dataset = preprocessTimeGroupFields(dataset, property.getGroupField());

        // 构建分组列 - 支持多个分组字段
        org.apache.spark.sql.Column[] groupColumns = groupFields.stream()
                .map(field -> getGroupColumn(field, property.getGroupField()))
                .toArray(org.apache.spark.sql.Column[]::new);

        // 执行分组统计，结果会包含所有分组字段 + 统计字段
        Dataset<Row> result;
        switch (statisticType) {
            case COUNT:
                result = dataset.groupBy(groupColumns)
                        .agg(functions.count(statisticField).alias(outputField));
                break;
            case SUM:
                result = dataset.groupBy(groupColumns)
                        .agg(functions.sum(statisticField).alias(outputField));
                break;
            case AVG:
                result = dataset.groupBy(groupColumns)
                        .agg(functions.avg(statisticField).alias(outputField));
                break;
            case MAX:
                result = dataset.groupBy(groupColumns)
                        .agg(functions.max(statisticField).alias(outputField));
                break;
            case MIN:
                result = dataset.groupBy(groupColumns)
                        .agg(functions.min(statisticField).alias(outputField));
                break;
            default:
                throw new RuntimeException("不支持的统计类型: " + statisticType);
        }

        // 确保结果包含所有原始分组字段和统计结果字段
        // 对于时间分组字段，需要选择原始字段而不是分组辅助字段
        List<org.apache.spark.sql.Column> selectColumns = new ArrayList<>();

        // 添加所有原始分组字段
        for (String groupField : groupFields) {
            selectColumns.add(functions.col(groupField));
        }

        // 添加统计结果字段
        selectColumns.add(functions.col(outputField));

        return result.select(selectColumns.toArray(new org.apache.spark.sql.Column[0]));
    }

    /**
     * 预处理时间分组字段
     * 为时间字段添加分组辅助列
     *
     * @param dataset 数据集
     * @param groupFields 分组字段配置
     * @return 处理后的数据集
     */
    private Dataset<Row> preprocessTimeGroupFields(Dataset<Row> dataset, List<GroupField> groupFields) {
        Dataset<Row> result = dataset;

        for (GroupField groupField : groupFields) {
            if (groupField.getGroupType() != null && groupField.getGroupType() > 0) {
                // 时间字段需要特殊处理
                String fieldName = groupField.getEnName();
                String groupColumnName = fieldName + "_group";

                // 根据分组类型添加分组列
                org.apache.spark.sql.Column groupColumn = createTimeGroupColumn(fieldName, groupField.getGroupType());
                result = result.withColumn(groupColumnName, groupColumn);
            }
        }

        return result;
    }

    /**
     * 创建时间分组列
     *
     * @param fieldName 字段名
     * @param groupType 分组类型 1-秒 2-分 3-时 4-天 5-周 6-月 7-年
     * @return 分组列
     */
    private org.apache.spark.sql.Column createTimeGroupColumn(String fieldName, Integer groupType) {
        org.apache.spark.sql.Column timeCol = functions.col(fieldName);

        switch (groupType) {
            case 1: // 秒
                return functions.date_trunc("second", timeCol);
            case 2: // 分
                return functions.date_trunc("minute", timeCol);
            case 3: // 时
                return functions.date_trunc("hour", timeCol);
            case 4: // 天
                return functions.date_trunc("day", timeCol);
            case 5: // 周
                return functions.date_trunc("week", timeCol);
            case 6: // 月
                return functions.date_trunc("month", timeCol);
            case 7: // 年
                return functions.date_trunc("year", timeCol);
            default:
                return timeCol;
        }
    }

    /**
     * 获取分组列
     *
     * @param fieldName 字段名
     * @param groupFields 分组字段配置
     * @return 分组列
     */
    private org.apache.spark.sql.Column getGroupColumn(String fieldName, List<GroupField> groupFields) {
        // 查找对应的分组字段配置
        for (GroupField groupField : groupFields) {
            if (fieldName.equals(groupField.getEnName())) {
                if (groupField.getGroupType() != null && groupField.getGroupType() > 0) {
                    // 使用时间分组列
                    return functions.col(fieldName + "_group");
                }
                break;
            }
        }
        // 普通字段直接使用原列
        return functions.col(fieldName);
    }

    /**
     * 验证分组字段是否存在于数据集中
     *
     * @param dataset 数据集
     * @param groupFields 分组字段列表
     * @throws RuntimeException 如果某个分组字段不存在
     */
    private void validateGroupFields(Dataset<Row> dataset, List<String> groupFields) {
        String[] datasetColumns = dataset.columns();
        List<String> availableColumns = java.util.Arrays.asList(datasetColumns);

        for (String groupField : groupFields) {
            if (!availableColumns.contains(groupField)) {
                throw new RuntimeException(String.format("分组字段 '%s' 在数据集中不存在。可用字段: %s",
                    groupField, String.join(", ", availableColumns)));
            }
        }
    }
}
