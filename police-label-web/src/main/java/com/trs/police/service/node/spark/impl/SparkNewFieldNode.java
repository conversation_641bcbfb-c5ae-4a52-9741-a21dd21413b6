package com.trs.police.service.node.spark.impl;

import com.trs.police.dto.node.properties.NewFieldProperties;
import com.trs.police.dto.node.properties.bean.NewField;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;

import java.util.List;

/**
 * Spark新字段节点
 *
 * <AUTHOR>
 */
public class SparkNewFieldNode extends SparkNode {

    public SparkNewFieldNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        NewFieldProperties property = getPropertyAs(NewFieldProperties.class);
        NodeData input = inputNode.get(0);
        
        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);
        
        // 应用新字段操作
        Dataset<Row> newFieldDataset = applySparkNewFields(dataset, property, context);
        
        // 转换回NodeData
        NodeData result = datasetToNodeData(newFieldDataset, input);
        result.setNodeMeta(nodeMeta);
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.NEW_FIELD;
    }

    /**
     * 应用Spark新字段操作
     *
     * @param dataset 数据集
     * @param property 新字段属性
     * @param context 上下文
     * @return 添加新字段后的数据集
     */
    private Dataset<Row> applySparkNewFields(Dataset<Row> dataset, NewFieldProperties property, NodeContext context) {
        if (property.getOutValue() == null || property.getOutValue().isEmpty()) {
            return dataset;
        }

        Dataset<Row> result = dataset;

        // 为每个新字段添加列
        for (NewField newField : property.getOutValue()) {
            result = addNewField(result, newField, context);
        }

        return result;
    }

    /**
     * 添加新字段
     *
     * @param dataset 数据集
     * @param newField 新字段定义
     * @param context 上下文
     * @return 添加字段后的数据集
     */
    private Dataset<Row> addNewField(Dataset<Row> dataset, NewField newField, NodeContext context) {
        String fieldName = newField.getEnName();
        String fieldValue = newField.getValue();
        String fieldType = newField.getTypeCode();

        // 直接使用value作为常量值添加新字段，与原始NewFieldNode逻辑保持一致
        org.apache.spark.sql.Column column = functions.lit(fieldValue);

        // 根据字段类型进行类型转换
        column = castToFieldType(column, fieldType);

        return dataset.withColumn(fieldName, column);
    }





    /**
     * 根据字段类型进行类型转换
     *
     * @param column 列
     * @param typeCode 字段类型编码
     * @return 转换后的列
     */
    private org.apache.spark.sql.Column castToFieldType(org.apache.spark.sql.Column column, String typeCode) {
        if (typeCode == null) {
            return column;
        }

        // 根据DataBaseFieldMappingType的类型编码进行转换
        switch (typeCode) {
            case "1": // 字符串
                return column.cast("string");
            case "2": // 整数
                return column.cast("long");
            case "3": // 小数
                return column.cast("double");
            case "4": // 日期
                return column.cast("date");
            case "5": // 时间戳
                return column.cast("timestamp");
            default:
                return column;
        }
    }
}
