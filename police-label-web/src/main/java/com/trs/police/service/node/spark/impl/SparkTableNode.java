package com.trs.police.service.node.spark.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.EmtpyExpression;
import com.trs.common.utils.expression.Expression;
import com.trs.police.dto.node.properties.TableNodeProperties;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.DataTableOverviewDto;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.FilterNodeConditionParser;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.datatable.DataTableService;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.fieldsService.FieldsService;
import com.trs.police.vo.DataTableFieldVO;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.police.entity.datasource.DataSource;
import com.trs.police.entity.datasource.DataSourceType;
import com.trs.police.entity.datasource.SourceInfo;
import com.trs.police.entity.datasource.AbstractDbSourceInfo;
import com.trs.police.entity.datatable.DataTable;
import com.trs.police.mapper.DataTableMapper;
import com.trs.police.mapper.DataSourceMapper;
import com.trs.police.utils.SourceInfoConverter;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.DataFrameReader;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Spark表格输入节点
 *
 * <AUTHOR>
 */
@Slf4j
public class SparkTableNode extends SparkNode {

    private DataTableService dataTableService;
    private FieldsService fieldsService;
    private DataTableMapper dataTableMapper;
    private DataSourceMapper dataSourceMapper;

    public SparkTableNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark,
                         DataTableService dataTableService, FieldsService fieldsService) {
        super(nodeMeta, nodeProperties, spark);
        this.dataTableService = dataTableService;
        this.fieldsService = fieldsService;
        this.dataTableMapper = BeanFactoryHolder.getBean(DataTableMapper.class).get();
        this.dataSourceMapper = BeanFactoryHolder.getBean(DataSourceMapper.class).get();
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        TableNodeProperties property = getPropertyAs(TableNodeProperties.class);

        List<DataTableFieldVO> fieldInfo = fieldsService.getFieldInfo(property.getTableId().intValue(), 1);
        AtomicInteger atomicInteger = new AtomicInteger(0);
        List<FieldInfoVO> header = fieldInfo.stream()
                .map(f -> {
                    FieldInfoVO field = new FieldInfoVO();
                    field.setCnName(f.getFieldNameCn());
                    field.setEnName(f.getFieldName());
                    field.setTypeCode(f.getFieldType());
                    field.setColIndex(atomicInteger.getAndIncrement());
                    return field;
                })
                .collect(Collectors.toList());

        NodeData nodeData = new NodeData();
        nodeData.setHeader(header);

        // 构造条件
        FilterNodeConditionParser parser = new FilterNodeConditionParser(context);
        Expression expression = StringUtils.isNotEmpty(property.getTokens())
                ? parser.parseCondition(property.getTokens())
                : new EmtpyExpression();

        try {
            // 通过Spark直接查询数据库
            Dataset<Row> sparkDataset = queryDataDirectlyWithSpark(property.getTableId(), expression);

            // 缓存数据集以提高性能
            sparkDataset = sparkDataset.cache();

            // 转换回NodeData格式
            NodeData result = datasetToNodeData(sparkDataset, nodeData);
            result.setTotalCount(sparkDataset.count());

            return result;
        } catch (Exception e) {
            log.warn("Spark直接查询失败，回退到原始方式: {}", e.getMessage());
            // 回退到原始方式
            return processWithFallback(property, header, expression, nodeData);
        }
    }

    @Override
    public Integer nodeType() {
        return NodeType.TABLE;
    }

    /**
     * 通过Spark直接查询数据库
     *
     * @param tableId 表ID
     * @param expression 过滤条件
     * @return Spark Dataset
     */
    private Dataset<Row> queryDataDirectlyWithSpark(Long tableId, Expression expression) {
        // 获取数据表信息
        DataTable dataTable = dataTableMapper.selectById(tableId);
        if (dataTable == null) {
            throw new RuntimeException("数据表不存在: " + tableId);
        }

        // 获取数据源信息
        DataSource dataSource = dataSourceMapper.selectById(dataTable.getDataSourceId());
        if (dataSource == null) {
            throw new RuntimeException("数据源不存在: " + dataTable.getDataSourceId());
        }

        // 解析数据源信息
        SourceInfo sourceInfo = SourceInfoConverter.fromJson(dataSource.getSourceInfo(), dataSource.getType());
        if (!(sourceInfo instanceof AbstractDbSourceInfo)) {
            throw new RuntimeException("不支持的数据源类型，仅支持数据库类型: " + dataSource.getType());
        }

        AbstractDbSourceInfo dbSourceInfo = (AbstractDbSourceInfo) sourceInfo;

        // 构建JDBC连接信息
        String jdbcUrl = buildJdbcUrl(dbSourceInfo);
        Properties connectionProperties = buildConnectionProperties(dbSourceInfo);

        // 构建查询SQL
        String tableName = dataTable.getTableName();
        String whereClause = convertExpressionToSql(expression);

        // 获取限制数量
        Integer count = BeanFactoryHolder.getEnv().getProperty("label.web.node.table.default.count", Integer.class, 2000);

        // 构建完整的SQL查询
        String query = buildSelectQuery(tableName, whereClause, count);

        log.info("Spark直接查询SQL: {}", query);

        // 使用Spark读取数据
        DataFrameReader reader = spark.read()
                .format("jdbc")
                .option("url", jdbcUrl)
                .option("query", query)
                .option("driver", getJdbcDriver(dataSource.getType()));

        // 设置连接属性
        for (String key : connectionProperties.stringPropertyNames()) {
            reader = reader.option(key, connectionProperties.getProperty(key));
        }

        return reader.load();
    }

    /**
     * 构建JDBC URL
     *
     * @param dbSourceInfo 数据库源信息
     * @return JDBC URL
     */
    private String buildJdbcUrl(AbstractDbSourceInfo dbSourceInfo) {
        String dbType = dbSourceInfo.getDbType();
        String host = dbSourceInfo.getHost();
        String port = dbSourceInfo.getPort();
        String dbName = dbSourceInfo.getDbName();

        switch (dbType.toUpperCase()) {
            case "MYSQL":
                return String.format("********************************************************************************************",
                    host, port, dbName);
            case "ORACLE":
                return String.format("**************************", host, port, dbName);
            case "POSTGRESQL":
                return String.format("jdbc:postgresql://%s:%s/%s", host, port, dbName);
            case "CLICKHOUSE":
                return String.format("jdbc:clickhouse://%s:%s/%s", host, port, dbName);
            default:
                throw new RuntimeException("不支持的数据库类型: " + dbType);
        }
    }

    /**
     * 构建连接属性
     *
     * @param dbSourceInfo 数据库源信息
     * @return 连接属性
     */
    private Properties buildConnectionProperties(AbstractDbSourceInfo dbSourceInfo) {
        Properties props = new Properties();
        props.setProperty("user", dbSourceInfo.getUserName());
        props.setProperty("password", dbSourceInfo.getPassword());

        // 设置连接池相关属性
        props.setProperty("numPartitions", "4");
        props.setProperty("fetchsize", "1000");

        return props;
    }

    /**
     * 获取JDBC驱动类名
     *
     * @param dataSourceType 数据源类型
     * @return 驱动类名
     */
    private String getJdbcDriver(DataSourceType dataSourceType) {
        switch (dataSourceType) {
            case MYSQL:
                return "com.mysql.cj.jdbc.Driver";
            case ORACLE:
                return "oracle.jdbc.driver.OracleDriver";
            case POSTGRESQL:
                return "org.postgresql.Driver";
            case CLICKHOUSE:
                return "ru.yandex.clickhouse.ClickHouseDriver";
            default:
                throw new RuntimeException("不支持的数据源类型: " + dataSourceType);
        }
    }

    /**
     * 构建SELECT查询语句
     *
     * @param tableName 表名
     * @param whereClause WHERE子句
     * @param limit 限制数量
     * @return SQL查询语句
     */
    private String buildSelectQuery(String tableName, String whereClause, Integer limit) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ").append(tableName);

        if (StringUtils.isNotEmpty(whereClause)) {
            sql.append(" WHERE ").append(whereClause);
        }

        if (limit != null && limit > 0) {
            sql.append(" LIMIT ").append(limit);
        }

        return sql.toString();
    }

    /**
     * 将Expression转换为SQL WHERE子句
     *
     * @param expression 表达式
     * @return SQL WHERE子句
     */
    private String convertExpressionToSql(Expression expression) {
        if (expression == null || expression instanceof EmtpyExpression) {
            return "";
        }

        try {
            return convertExpressionToSqlRecursive(expression);
        } catch (Exception e) {
            log.warn("Expression到SQL转换失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 递归转换Expression为SQL
     *
     * @param expression 表达式
     * @return SQL字符串
     */
    private String convertExpressionToSqlRecursive(Expression expression) {
        if (expression == null) {
            return "";
        }

        String className = expression.getClass().getSimpleName();

        try {
            // 使用反射获取Expression的属性
            switch (className) {
                case "AndExpression":
                    return convertAndExpression(expression);
                case "OrExpression":
                    return convertOrExpression(expression);
                case "NotExpression":
                    return convertNotExpression(expression);
                case "ConditionExpression":
                case "SimpleConditionExpression":
                    return convertConditionExpression(expression);
                case "EmtpyExpression":
                    return "";
                default:
                    // 尝试通用方法
                    return convertGenericExpression(expression);
            }
        } catch (Exception e) {
            log.debug("转换Expression失败，类型: {}, 错误: {}", className, e.getMessage());
            return "";
        }
    }

    /**
     * 转换AND表达式
     *
     * @param expression AND表达式
     * @return SQL字符串
     */
    private String convertAndExpression(Expression expression) {
        try {
            // 使用反射获取左右子表达式
            java.lang.reflect.Field leftField = expression.getClass().getDeclaredField("left");
            java.lang.reflect.Field rightField = expression.getClass().getDeclaredField("right");
            leftField.setAccessible(true);
            rightField.setAccessible(true);

            Expression left = (Expression) leftField.get(expression);
            Expression right = (Expression) rightField.get(expression);

            String leftSql = convertExpressionToSqlRecursive(left);
            String rightSql = convertExpressionToSqlRecursive(right);

            if (StringUtils.isNotEmpty(leftSql) && StringUtils.isNotEmpty(rightSql)) {
                return "(" + leftSql + " AND " + rightSql + ")";
            } else if (StringUtils.isNotEmpty(leftSql)) {
                return leftSql;
            } else if (StringUtils.isNotEmpty(rightSql)) {
                return rightSql;
            }
        } catch (Exception e) {
            log.debug("转换AND表达式失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 转换OR表达式
     *
     * @param expression OR表达式
     * @return SQL字符串
     */
    private String convertOrExpression(Expression expression) {
        try {
            // 使用反射获取左右子表达式
            java.lang.reflect.Field leftField = expression.getClass().getDeclaredField("left");
            java.lang.reflect.Field rightField = expression.getClass().getDeclaredField("right");
            leftField.setAccessible(true);
            rightField.setAccessible(true);

            Expression left = (Expression) leftField.get(expression);
            Expression right = (Expression) rightField.get(expression);

            String leftSql = convertExpressionToSqlRecursive(left);
            String rightSql = convertExpressionToSqlRecursive(right);

            if (StringUtils.isNotEmpty(leftSql) && StringUtils.isNotEmpty(rightSql)) {
                return "(" + leftSql + " OR " + rightSql + ")";
            } else if (StringUtils.isNotEmpty(leftSql)) {
                return leftSql;
            } else if (StringUtils.isNotEmpty(rightSql)) {
                return rightSql;
            }
        } catch (Exception e) {
            log.debug("转换OR表达式失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 转换NOT表达式
     *
     * @param expression NOT表达式
     * @return SQL字符串
     */
    private String convertNotExpression(Expression expression) {
        try {
            // 使用反射获取子表达式
            java.lang.reflect.Field childField = expression.getClass().getDeclaredField("expression");
            childField.setAccessible(true);

            Expression child = (Expression) childField.get(expression);
            String childSql = convertExpressionToSqlRecursive(child);

            if (StringUtils.isNotEmpty(childSql)) {
                return "NOT (" + childSql + ")";
            }
        } catch (Exception e) {
            log.debug("转换NOT表达式失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 转换条件表达式
     *
     * @param expression 条件表达式
     * @return SQL字符串
     */
    private String convertConditionExpression(Expression expression) {
        try {
            // 尝试获取字段名、操作符和值
            String fieldName = getFieldFromExpression(expression);
            String operator = getOperatorFromExpression(expression);
            Object[] values = getValuesFromExpression(expression);

            if (StringUtils.isNotEmpty(fieldName) && StringUtils.isNotEmpty(operator) && values != null) {
                return buildSqlCondition(fieldName, operator, values);
            }
        } catch (Exception e) {
            log.debug("转换条件表达式失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 转换通用表达式
     *
     * @param expression 表达式
     * @return SQL字符串
     */
    private String convertGenericExpression(Expression expression) {
        try {
            // 尝试通过toString方法获取表达式信息
            String expressionStr = expression.toString();
            log.debug("通用表达式转换: {}", expressionStr);

            // 这里可以根据toString的格式进行解析
            // 由于不同的Expression实现toString格式可能不同，这里只是示例
            return "";
        } catch (Exception e) {
            log.debug("转换通用表达式失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 从Expression中提取字段名
     *
     * @param expression 表达式
     * @return 字段名
     */
    private String getFieldFromExpression(Expression expression) {
        try {
            // 尝试多种可能的字段名属性
            String[] fieldNames = {"field", "fieldName", "key", "name", "attribute"};

            for (String fieldName : fieldNames) {
                try {
                    java.lang.reflect.Field field = expression.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object value = field.get(expression);
                    if (value != null) {
                        return value.toString();
                    }
                } catch (NoSuchFieldException ignored) {
                    // 继续尝试下一个字段名
                }
            }

            // 尝试通过方法获取
            String[] methodNames = {"getField", "getFieldName", "getKey", "getName", "getAttribute"};
            for (String methodName : methodNames) {
                try {
                    java.lang.reflect.Method method = expression.getClass().getDeclaredMethod(methodName);
                    method.setAccessible(true);
                    Object value = method.invoke(expression);
                    if (value != null) {
                        return value.toString();
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个方法
                }
            }
        } catch (Exception e) {
            log.debug("提取字段名失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从Expression中提取操作符
     *
     * @param expression 表达式
     * @return 操作符
     */
    private String getOperatorFromExpression(Expression expression) {
        try {
            // 尝试多种可能的操作符属性
            String[] operatorFields = {"operator", "op", "operation"};

            for (String fieldName : operatorFields) {
                try {
                    java.lang.reflect.Field field = expression.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object value = field.get(expression);
                    if (value != null) {
                        // 如果是IOperator对象，尝试获取其字符串表示
                        if (value.getClass().getSimpleName().contains("Operator")) {
                            return getOperatorString(value);
                        }
                        return value.toString();
                    }
                } catch (NoSuchFieldException ignored) {
                    // 继续尝试下一个字段名
                }
            }

            // 尝试通过方法获取
            String[] methodNames = {"getOperator", "getOp", "getOperation"};
            for (String methodName : methodNames) {
                try {
                    java.lang.reflect.Method method = expression.getClass().getDeclaredMethod(methodName);
                    method.setAccessible(true);
                    Object value = method.invoke(expression);
                    if (value != null) {
                        if (value.getClass().getSimpleName().contains("Operator")) {
                            return getOperatorString(value);
                        }
                        return value.toString();
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个方法
                }
            }
        } catch (Exception e) {
            log.debug("提取操作符失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从Expression中提取值数组
     *
     * @param expression 表达式
     * @return 值数组
     */
    private Object[] getValuesFromExpression(Expression expression) {
        try {
            // 尝试多种可能的值属性
            String[] valueFields = {"values", "value", "data", "params"};

            for (String fieldName : valueFields) {
                try {
                    java.lang.reflect.Field field = expression.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object value = field.get(expression);
                    if (value != null) {
                        if (value instanceof Object[]) {
                            return (Object[]) value;
                        } else if (value instanceof java.util.List) {
                            java.util.List<?> list = (java.util.List<?>) value;
                            return list.toArray();
                        } else {
                            return new Object[]{value};
                        }
                    }
                } catch (NoSuchFieldException ignored) {
                    // 继续尝试下一个字段名
                }
            }

            // 尝试通过方法获取
            String[] methodNames = {"getValues", "getValue", "getData", "getParams"};
            for (String methodName : methodNames) {
                try {
                    java.lang.reflect.Method method = expression.getClass().getDeclaredMethod(methodName);
                    method.setAccessible(true);
                    Object value = method.invoke(expression);
                    if (value != null) {
                        if (value instanceof Object[]) {
                            return (Object[]) value;
                        } else if (value instanceof java.util.List) {
                            java.util.List<?> list = (java.util.List<?>) value;
                            return list.toArray();
                        } else {
                            return new Object[]{value};
                        }
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个方法
                }
            }
        } catch (Exception e) {
            log.debug("提取值失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取操作符的字符串表示
     *
     * @param operator 操作符对象
     * @return 操作符字符串
     */
    private String getOperatorString(Object operator) {
        try {
            // 尝试获取操作符的字符串表示
            String[] methodNames = {"getOperator", "getSymbol", "toString", "name"};
            for (String methodName : methodNames) {
                try {
                    java.lang.reflect.Method method = operator.getClass().getDeclaredMethod(methodName);
                    method.setAccessible(true);
                    Object value = method.invoke(operator);
                    if (value != null) {
                        return value.toString();
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个方法
                }
            }

            // 如果是枚举类型，尝试获取name
            if (operator instanceof Enum) {
                return ((Enum<?>) operator).name();
            }

            return operator.toString();
        } catch (Exception e) {
            log.debug("获取操作符字符串失败: {}", e.getMessage());
            return operator.toString();
        }
    }

    /**
     * 构建SQL条件
     *
     * @param fieldName 字段名
     * @param operator 操作符
     * @param values 值数组
     * @return SQL条件字符串
     */
    private String buildSqlCondition(String fieldName, String operator, Object[] values) {
        if (StringUtils.isEmpty(fieldName) || StringUtils.isEmpty(operator) || values == null || values.length == 0) {
            return "";
        }

        // 转换操作符为SQL操作符
        String sqlOperator = convertOperatorToSql(operator);
        if (StringUtils.isEmpty(sqlOperator)) {
            return "";
        }

        StringBuilder condition = new StringBuilder();
        condition.append(fieldName).append(" ");

        switch (sqlOperator.toUpperCase()) {
            case "=":
            case "!=":
            case "<>":
            case ">":
            case ">=":
            case "<":
            case "<=":
                condition.append(sqlOperator).append(" ").append(formatSqlValue(values[0]));
                break;
            case "LIKE":
            case "NOT LIKE":
                condition.append(sqlOperator).append(" ").append(formatSqlValue(values[0]));
                break;
            case "IN":
            case "NOT IN":
                condition.append(sqlOperator).append(" (");
                for (int i = 0; i < values.length; i++) {
                    if (i > 0) {
                        condition.append(", ");
                    }
                    condition.append(formatSqlValue(values[i]));
                }
                condition.append(")");
                break;
            case "BETWEEN":
                if (values.length >= 2) {
                    condition.append("BETWEEN ").append(formatSqlValue(values[0]))
                            .append(" AND ").append(formatSqlValue(values[1]));
                }
                break;
            case "IS NULL":
                condition.append("IS NULL");
                break;
            case "IS NOT NULL":
                condition.append("IS NOT NULL");
                break;
            default:
                // 默认处理
                condition.append(sqlOperator).append(" ").append(formatSqlValue(values[0]));
                break;
        }

        return condition.toString();
    }

    /**
     * 转换操作符为SQL操作符
     *
     * @param operator 原始操作符
     * @return SQL操作符
     */
    private String convertOperatorToSql(String operator) {
        if (StringUtils.isEmpty(operator)) {
            return "";
        }

        // 常见操作符映射
        switch (operator.toUpperCase()) {
            case "EQ":
            case "EQUAL":
            case "EQUALS":
                return "=";
            case "NE":
            case "NOT_EQUAL":
            case "NOT_EQUALS":
                return "!=";
            case "GT":
            case "GREATER_THAN":
                return ">";
            case "GE":
            case "GREATER_EQUAL":
            case "GREATER_THAN_OR_EQUAL":
                return ">=";
            case "LT":
            case "LESS_THAN":
                return "<";
            case "LE":
            case "LESS_EQUAL":
            case "LESS_THAN_OR_EQUAL":
                return "<=";
            case "LIKE":
            case "CONTAINS":
                return "LIKE";
            case "NOT_LIKE":
            case "NOT_CONTAINS":
                return "NOT LIKE";
            case "IN":
                return "IN";
            case "NOT_IN":
                return "NOT IN";
            case "BETWEEN":
                return "BETWEEN";
            case "IS_NULL":
            case "NULL":
                return "IS NULL";
            case "IS_NOT_NULL":
            case "NOT_NULL":
                return "IS NOT NULL";
            default:
                // 如果已经是SQL操作符，直接返回
                if (operator.matches("^(=|!=|<>|>|>=|<|<=|LIKE|NOT LIKE|IN|NOT IN|BETWEEN|IS NULL|IS NOT NULL)$")) {
                    return operator;
                }
                log.debug("未知操作符: {}", operator);
                return "";
        }
    }

    /**
     * 格式化SQL值
     *
     * @param value 值
     * @return 格式化后的SQL值
     */
    private String formatSqlValue(Object value) {
        if (value == null) {
            return "NULL";
        }

        if (value instanceof String) {
            // 字符串值需要加单引号，并转义单引号
            String strValue = value.toString().replace("'", "''");
            return "'" + strValue + "'";
        } else if (value instanceof Number) {
            // 数字值直接返回
            return value.toString();
        } else if (value instanceof Boolean) {
            // 布尔值转换
            return ((Boolean) value) ? "1" : "0";
        } else if (value instanceof java.util.Date) {
            // 日期值格式化
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return "'" + sdf.format((java.util.Date) value) + "'";
        } else {
            // 其他类型转换为字符串
            String strValue = value.toString().replace("'", "''");
            return "'" + strValue + "'";
        }
    }

    /**
     * 回退到原始方式处理数据
     *
     * @param property 表节点属性
     * @param header 表头信息
     * @param expression 过滤表达式
     * @param nodeData 节点数据
     * @return 处理结果
     */
    private NodeData processWithFallback(TableNodeProperties property, List<FieldInfoVO> header,
                                       Expression expression, NodeData nodeData) {
        DataTableOverviewDto dto = new DataTableOverviewDto();
        dto.setTableId(property.getTableId());
        Integer count = BeanFactoryHolder.getEnv().getProperty("label.web.node.table.default.count", Integer.class, 2000);
        dto.setPageSize(count);

        RestfulResultsV2<JSONObject> dataOverview = dataTableService.getData(dto, expression);
        List<JSONObject> datas = dataOverview.getDatas();

        // 使用Spark处理数据
        Dataset<Row> sparkDataset = createSparkDatasetFromJsonData(datas, header);

        // 缓存数据集
        sparkDataset = sparkDataset.cache();

        // 转换回NodeData格式
        NodeData result = datasetToNodeData(sparkDataset, nodeData);
        result.setTotalCount(dataOverview.getSummary().getTotal());

        return result;
    }

    /**
     * 从JSON数据创建Spark Dataset
     *
     * @param jsonData JSON数据列表
     * @param header 表头信息
     * @return Spark Dataset
     */
    private Dataset<Row> createSparkDatasetFromJsonData(List<JSONObject> jsonData, List<FieldInfoVO> header) {
        // 将JSON数据转换为NodeData格式，然后转换为Dataset
        List<List<FieldValue>> data = jsonData.stream()
                .map(d -> mapToFieldValue(header, d))
                .collect(Collectors.toList());

        NodeData tempNodeData = new NodeData();
        tempNodeData.setHeader(header);
        tempNodeData.setData(data);
        tempNodeData.setTotalCount((long) data.size());

        return nodeDataToDataset(tempNodeData);
    }

    /**
     * 将JSON对象映射为FieldValue列表
     *
     * @param header 表头信息
     * @param jsonObject JSON对象
     * @return FieldValue列表
     */
    private List<FieldValue> mapToFieldValue(List<FieldInfoVO> header, JSONObject jsonObject) {
        return header.stream()
                .map(h -> {
                    FieldValue fieldValue = new FieldValue();
                    fieldValue.setEnName(h.getEnName());
                    fieldValue.setColIndex(h.getColIndex());
                    Object value = jsonObject.get(h.getEnName());
                    fieldValue.setValue(value != null ? value.toString() : null);
                    return fieldValue;
                })
                .collect(Collectors.toList());
    }
}
